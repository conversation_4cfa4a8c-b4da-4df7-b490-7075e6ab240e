/*
 * mobilebackup2.h
 * Definitions for the mobilebackup2 service (iOS4+)
 *
 * Copyright (c) 2010-2019 <PERSON><PERSON>, All Rights Reserved.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

#ifndef __MOBILEBACKUP2_H
#define __MOBILEBACKUP2_H

#include "idevice.h"
#include "libimobiledevice/mobilebackup2.h"
#include "device_link_service.h"

struct mobilebackup2_client_private {
	device_link_service_client_t parent;
};

#endif
