#!/bin/sh
SRCDIR=`dirname $0`
if test -n "$1"; then
  VER=$1
else
  if test -r "${SRCDIR}/.git" && test -x "`which git`" ; then
    git update-index -q --refresh
    if ! VER=`git describe --tags --dirty 2>/dev/null`; then
      COMMIT=`git rev-parse --short HEAD`
      DIRTY=`git diff --quiet HEAD || echo "-dirty"`
      VER=`sed -n '1,/RE/s/Version \(.*\)/\1/p' ${SRCDIR}/NEWS`-git-${COMMIT}${DIRTY}
    fi
  else
    if test -f "${SRCDIR}/.tarball-version"; then
      VER=`cat "${SRCDIR}/.tarball-version"`
    fi
  fi
fi
VER=`printf %s "$VER" | head -n1`
printf %s "$VER"
