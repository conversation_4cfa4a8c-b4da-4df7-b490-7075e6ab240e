.TH "idevicepair" 1
.SH NAME
idevicepair \- Manage host pairings with devices and usbmuxd.
.SH SYNOPSIS
.B idevicepair
[OPTIONS] COMMAND

.SH DESCRIPTION

Manage host pairings with devices and usbmuxd.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-w, \-\-wireless
perform wireless pairing (\f[B]see NOTE\f[]).
.TP
.B \-n, \-\-network
connect to network device (\f[B]see NOTE\f[]).
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-h, \-\-help
prints usage information.
.TP
.B \-v, \-\-version
prints version information.

.SH COMMANDS
.TP
.B systembuid
print the system buid of the usbmuxd host.
.TP
.B hostid
print the host id for target device.
.TP
.B pair
pair device with this host.
.TP
.B validate
validate if device is paired with this host.
.TP
.B unpair
unpair device with this host.
.TP
.B list
list devices paired with this host.

.SH NOTE
Pairing over network (wireless pairing) is only supported by Apple TV
devices. To perform a wireless pairing, you need to use the \f[B]\-w\f[]
command line switch.

Make sure to put the device into pairing mode first by opening
Settings > Remotes and Devices > Remote App and Devices.

The pairable device will become visible with a special UDID, and then you
can run idevicepair like this:

.B idevicepair -u fffc8:ab:cd:12:34:56fff -w pair

idevicepair will then ask for the PIN that the device is displaying and
continues with the pairing once entered.

Please note that wireless pairing is currently not supported on Linux.

.SH AUTHORS
Nikias Bassen

Martin Szulecki

Julien Lavergne

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
