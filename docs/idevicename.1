.TH "idevicename" 1
.SH NAME
idevicename \- Display the device name or set it to NAME if specified.
.SH SYNOPSIS
.B idevicename
[OPTIONS] [NAME]

.SH DESCRIPTION

Simple utility to manage the device name.

If called without any extra argument this tool will print the current device name.

If
.B NAME
is given the device name will be set to the name specified.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
Nikias Bassen

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
