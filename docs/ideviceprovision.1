.TH "ideviceprovision" 1
.SH NAME
ideviceprovision \- Manage provisioning profiles on a device.
.SH SYNOPSIS
.B ideviceprovision
[OPTIONS] COMMAND

.SH DESCRIPTION

Manage provisioning profiles on a device.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP 
.B \-x, \-\-xml
print XML output when using the 'dump' command.
.TP 
.B \-d, \-\-debug
enable communication debugging.
.TP 
.B \-h, \-\-help
prints usage information.
.TP
.B \-v, \-\-version
prints version information.

.SH COMMANDS
.TP
.B install FILE
Install the provisioning profile specified by FILE. A valid ".mobileprovision"
file is expected.
.TP
.B list
Get a list of all provisioning profiles on the device.
.TP
.B copy PATH
Retrieves all provisioning profiles from the device and stores them into the
existing directory specified by PATH. The files will be stored 
as "UUID.mobileprovision".
.TP
.B remove UUID
Removes the provisioning profile identified by UUID.
.TP
.B dump FILE
Prints detailed information about the provisioning profile specified by FILE.

.SH AUTHORS
Nikias Bassen

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
