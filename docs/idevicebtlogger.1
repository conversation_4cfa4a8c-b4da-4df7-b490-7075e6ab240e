.TH "idevicebtlogger" 1
.SH NAME
idevicebtlogger \- Capture HCI traffic of a connected device.
.SH SYNOPSIS
.B idevicebtlogger
[OPTIONS]
<FILE>

.SH DESCRIPTION

Capture HCI traffic of a connected device.  Requires Bluetooth logging profile to be installed on device with iOS 13 or higher. See https://www.bluetooth.com/blog/a-new-way-to-debug-iosbluetooth-applications/ for iOS device configuration.

The HCI traffic can be stored in Apple's native PacketLogger format or converted into PCAP format for live feedback in Wireshark. 

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID
.TP
.B \-n, \-\-network
connect to network device
.TP
.B \-f, \-\-format FORMAT
set log format: PacketLogger (default), or pcap
.TP
.B \-x, \-\-exit
exit when device disconnects
.TP
.B \-d, \-\-debug
enable communication debugging
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH EXAMPLES
.TP
.B idevicebtlogger \-u 00008030\-0000111ABC000DEF
Capture HCI traffic of device with UDID 00008030-0000111ABC000DEF.
.TP
.B idevicebtlogger \-x
Capture HCI traffic of device and exit when the device is unplugged.
.TP
.B idevicebtlogger \-f pcap
Capture HCI traffic of device in PCAP format. 
.TP
.B idevicebtlogger -f pcap - | wireshark -k -i -
Capture HCI traffic and pipe it into Wireshark for live feedback.

.SH AUTHORS
Geoffrey Kruse

Matthias Ringwald

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
