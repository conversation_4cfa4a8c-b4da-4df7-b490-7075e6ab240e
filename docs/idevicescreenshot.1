.TH "idevicescreenshot" 1
.SH NAME
idevicescreenshot \- Gets a screenshot from the connected device.
.SH SYNOPSIS
.B idevicescreenshot
[OPTIONS] [FILE]

.SH DESCRIPTION

Gets a screenshot from the connected device.

The screenshot is saved as a TIFF image with the given FILE name, where the
default name is "screenshot-DATE.tiff",
e.g.: ./screenshot-2013-12-31-23-59-59.tiff

NOTE: A mounted developer disk image is required on the device, otherwise
the screenshotr service is not available.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
Nikias Bassen

Man page written to conform with Debian by <PERSON>.

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
