.TH "idevicedate" 1
.SH NAME
idevicedate \- Display the current date or set it on a device.
.SH SYNOPSIS
.B idevicedate
[OPTIONS]

.SH DESCRIPTION

Simple utility to manage the clock on a device.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-s, \-\-set TIMESTAMP
set UTC time described by TIMESTAMP
.TP
.B \-c, \-\-sync
set time of device to current system time
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
<PERSON>

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
