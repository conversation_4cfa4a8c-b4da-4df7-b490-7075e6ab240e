.TH "idevicebackup" 1
.SH NAME
idevicebackup \- Create or restore backup for devices.
.SH SYNOPSIS
.B idevicebackup
[OPTIONS] CMD [DIRECTORY]

.SH DESCRIPTION

Create or restore backup in/from the specified directory.

\f[B]NOTE\f[]: This tool is outdated. See idevicebackup2(1) for an updated tool.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP 
.B \-h, \-\-help
prints usage information.
.TP
.B \-v, \-\-version
prints version information.

.SH COMMANDS
.TP
.B backup
saves a device backup into DIRECTORY.
.TP
.B restore
restores a device backup from DIRECTORY.

.SH AUTHORS
<PERSON>

Man page written to conform with Debian by <PERSON>.

.SH SEE ALSO
idevicebackup2(1)

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
