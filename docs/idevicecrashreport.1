.TH "idevicecrashreport" 1
.SH NAME
idevicecrashreport \- Retrieve crash reports from a device.
.SH SYNOPSIS
.B idevicecrashreport
[OPTIONS] DIRECTORY

.SH DESCRIPTION

Simple utility to move crash reports from a device to a local directory.

The utility outputs lines prefixed with either "Link:", "Copy:" or "Move:"
depending on whether a symlink was created, a file was copied or moved from
the device to the target DIRECTORY.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
Target specific device by UDID.
.TP
.B \-n, \-\-network
Connect to network device.
.TP
.B \-e, \-\-extract
Extract raw crash report into separate '.crash' files.
.TP
.B \-k, \-\-keep
Copy but do not remove crash reports from device.
.TP
.B \-d, \-\-debug
Enable communication debugging.
.TP
.B \-f, \-\-filter NAME
Filter crash reports by NAME (case sensitive)
.TP
.B \-\-remove\-all
Remove all crash log files without copying. Can be used with \f[B]-f\f[] to only remove matching files.
.TP
.B \-h, \-\-help
Prints usage information.
.TP
.B \-v, \-\-version
Prints version information.

.SH AUTHOR
Martin Szulecki

Nikias Bassen

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
