.TH "ideviceinfo" 1
.SH NAME
ideviceinfo \- Show information about the first connected device.
.SH SYNOPSIS
.B ideviceinfo
[OPTIONS]

.SH DESCRIPTION

Show information about the first connected device.

.SH OPTIONS
.TP
.B \-s, \-\-simple
use a simple connection to avoid auto-pairing with the device.
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-q, \-\-domain NAME
set domain of query to NAME. Default: None.
.TP
.B \-k, \-\-key NAME
only query key specified by NAME. Default: All keys.
.TP
.B \-x, \-\-xml
output information as xml plist instead of key/value pairs.
.TP
.B \-h, \-\-help
prints usage information.
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
<PERSON> page written to conform with <PERSON><PERSON> by <PERSON>.

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
