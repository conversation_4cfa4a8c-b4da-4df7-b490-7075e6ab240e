.TH "idevicesetlocation" 1
.SH NAME
idevicesetlocation \- Simulate location on iOS device.
.SH SYNOPSIS
.B idevicesetlocation
[OPTIONS] -- <LAT> <LONG>

.B idevicesetlocation
[OPTIONS] reset

.SH DESCRIPTION

Simulate location on iOS device with mounted developer disk image.

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID
.TP
.B \-n, \-\-network
connect to network device
.TP
.B \-d, \-\-debug
enable communication debugging
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
Nikias Bassen

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
