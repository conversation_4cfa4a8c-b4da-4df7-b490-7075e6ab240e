.TH "ideviceimagemounter" 1
.SH NAME
ideviceimagemounter \- Mount, list, or unmount a disk image on the device.
.SH SYNOPSIS
.B ideviceimagemounter
[OPTIONS] COMMAND [COMMAND OPTIONS]

.SH DESCRIPTION

Mount, list, or unmount a disk image on the device.

.SH COMMANDS
.TP
.B mount PATH
Mount the developer disk image at PATH.
For iOS 17+, PATH is a directory containing a .dmg image, a BuildManifest.plist,
and a Firmware sub-directory.

For older versions PATH is a .dmg filename with a .dmg.signature file in the same directory, or with
another parameter pointing to a file elsewhere.
.TP
.B list
List mounted disk images.
.TP
.B unmount PATH
Unmount the image mounted at PATH.
.TP
.B devmodestatus
Query the developer mode status (iOS 16+)

.SH OPTIONS
.TP
.B \-u, \-\-udid UDID
target specific device by UDID.
.TP
.B \-n, \-\-network
connect to network device.
.TP
.B \-d, \-\-debug
enable communication debugging.
.TP
.B \-t, \-\-imagetype NAME
the image type to use, default is 'Developer'
.TP
.B \-x, \-\-xml
use XML output
.TP
.B \-h, \-\-help
prints usage information
.TP
.B \-v, \-\-version
prints version information.

.SH AUTHOR
Nikias Bassen

Man page written to conform with Debian by Julien Lavergne.

.SH ON THE WEB
https://libimobiledevice.org

https://github.com/libimobiledevice/libimobiledevice
