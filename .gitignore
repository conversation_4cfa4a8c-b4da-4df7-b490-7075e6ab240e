# git-ls-files --others --exclude-from=.git/info/exclude
# Lines that start with '#' are comments.
# For a project mostly in C, the following would be a good set of
# exclude patterns (uncomment them if you want to use them):
*.[oa]
*~
*.po
*.lo
*.la
autom4te.cache/*
*.in
*/.deps/*
m4/*
swig/*
*.swp
*.patch
aclocal.m4
config.h
config.log
config.sub
config.guess
config.status
configure
depcomp
install-sh
compile
main
ltmain.sh
missing
mkinstalldirs
libtool
*Makefile
py-compile
stamp-h1
src/.libs
docs/html
libimobiledevice-1.0.pc
tools/.libs/*
tools/idevice*
tools/afcclient
!tools/idevice*.[ch]
cython/.libs/*
cython/*.c
doxygen.cfg
