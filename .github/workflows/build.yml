name: build

on:
  push:
  pull_request:
  schedule:
    - cron: '0 0 1 * *'

jobs:
  build-linux-ubuntu:
    runs-on: ubuntu-latest
    steps:
    - name: install dependencies
      run: |
          sudo apt-get update
          pip install cython
    - name: prepare environment
      run: |
          echo "target_triplet=`gcc -dumpmachine`" >> $GITHUB_ENV
    - name: fetch libplist
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libplist-latest_${{env.target_triplet}}
        repo: libimobiledevice/libplist
    - name: fetch libusbmuxd
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libusbmuxd-latest_${{env.target_triplet}}
        repo: libimobiledevice/libusbmuxd
    - name: fetch libimobiledevice-glue
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libimobiledevice-glue-latest_${{env.target_triplet}}
        repo: libimobiledevice/libimobiledevice-glue
    - name: fetch libtatsu
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libtatsu-latest_${{env.target_triplet}}
        repo: libimobiledevice/libtatsu
    - name: install external dependencies
      run: |
          mkdir extract
          for I in *.tar; do
            tar -C extract -xvf $I
          done
          sudo cp -r extract/* /
          sudo ldconfig
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: autogen
      run: ./autogen.sh PKG_CONFIG_PATH=/usr/local/lib/pkgconfig LDFLAGS="-Wl,-rpath=/usr/local/lib" --enable-debug
    - name: make
      run: make
    - name: make install
      run: sudo make install
    - name: prepare artifact
      run: |
          mkdir -p dest
          DESTDIR=`pwd`/dest make install
          tar -C dest -cf libimobiledevice.tar usr
    - name: publish artifact
      uses: actions/upload-artifact@v4
      with:
        name: libimobiledevice-latest_${{env.target_triplet}}
        path: libimobiledevice.tar
  build-macOS:
    runs-on: macOS-latest
    steps:
    - name: install dependencies
      run: |
          if test -x "`which port`"; then
            sudo port install libtool autoconf automake pkgconfig
          else
            brew install libtool autoconf automake pkgconfig
          fi
          pip3 install --break-system-packages cython
      shell: bash
    - name: fetch libplist
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libplist-latest_macOS
        repo: libimobiledevice/libplist
    - name: fetch libusbmuxd
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libusbmuxd-latest_macOS
        repo: libimobiledevice/libusbmuxd
    - name: fetch libimobiledevice-glue
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libimobiledevice-glue-latest_macOS
        repo: libimobiledevice/libimobiledevice-glue
    - name: fetch libtatsu
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libtatsu-latest_macOS
        repo: libimobiledevice/libtatsu
    - name: install external dependencies
      run: |
          mkdir extract
          for I in *.tar; do
            tar -C extract -xvf $I
          done
          sudo cp -r extract/* /
    - uses: actions/checkout@v4
    - name: install additional requirements
      run: |
          mkdir -p lib
          curl -o lib/libcrypto.35.tbd -Ls \
              https://gist.github.com/nikias/94c99fd145a75a5104415e5117b0cafa/raw/5209dfbff5a871a14272afe4794e76eb4cf6f062/libcrypto.35.tbd
          curl -o lib/libssl.35.tbd -Ls \
              https://gist.github.com/nikias/94c99fd145a75a5104415e5117b0cafa/raw/5209dfbff5a871a14272afe4794e76eb4cf6f062/libssl.35.tbd
          echo "LIBSSL=`pwd`/lib/libssl.35.tbd" >> $GITHUB_ENV
          echo "LIBCRYPTO=`pwd`/lib/libcrypto.35.tbd" >> $GITHUB_ENV
          LIBRESSL_VER=2.2.7
          echo "LIBRESSL_VER=$LIBRESSL_VER" >> $GITHUB_ENV
          FILENAME="libressl-$LIBRESSL_VER.tar.gz"
          curl -o $FILENAME -Ls "https://ftp.openbsd.org/pub/OpenBSD/LibreSSL/$FILENAME"
          mkdir -p deps
          tar -C deps -xzf $FILENAME
          echo "DEPSDIR=`pwd`/deps" >> $GITHUB_ENV
    - name: autogen
      run: |
          SDKDIR=`xcrun --sdk macosx --show-sdk-path`
          TESTARCHS="arm64 x86_64"
          USEARCHS=
          for ARCH in $TESTARCHS; do
            if echo "int main(int argc, char **argv) { return 0; }" |clang -arch $ARCH -o /dev/null -isysroot $SDKDIR -x c - 2>/dev/null; then
              USEARCHS="$USEARCHS -arch $ARCH"
            fi
          done
          export CFLAGS="$USEARCHS -isysroot $SDKDIR"
          echo "Using CFLAGS: $CFLAGS"
          PYTHON3_BIN=`xcrun -f python3`
          if test -x $PYTHON3_BIN; then
            export PYTHON=$PYTHON3_BIN
            PYTHON_VER=`$PYTHON3_BIN -c "import distutils.sysconfig; print(distutils.sysconfig.get_config_var('VERSION'))"`
            PYTHON_EXEC_PREFIX=`$PYTHON3_BIN -c "import distutils.sysconfig; print(distutils.sysconfig.get_config_var('exec_prefix'))"`
            PYTHON_LIBS_PATH=$PYTHON_EXEC_PREFIX/lib
            PYTHON_FRAMEWORK_PATH=$PYTHON_EXEC_PREFIX/Python3
            export PYTHON_CPPFLAGS="-I$PYTHON_EXEC_PREFIX/Headers"
            export PYTHON_LIBS="-L$PYTHON_LIBS_PATH -lpython$PYTHON_VER"
            export PYTHON_EXTRA_LDFLAGS="-Wl,-stack_size,1000000  -framework CoreFoundation $PYTHON_FRAMEWORK_PATH"
          fi
          ./autogen.sh PKG_CONFIG_PATH=/usr/local/lib/pkgconfig --enable-debug \
              openssl_CFLAGS="-I${{ env.DEPSDIR }}/libressl-${{ env.LIBRESSL_VER }}/include" \
              openssl_LIBS="-Xlinker ${{ env.LIBSSL }} -Xlinker ${{ env.LIBCRYPTO }}"
    - name: make
      run: make
    - name: make install
      run: sudo make install
    - name: prepare artifact
      run: |
          mkdir -p dest
          DESTDIR=`pwd`/dest make install
          tar -C dest -cf libimobiledevice.tar usr
    - name: publish artifact
      uses: actions/upload-artifact@v4
      with:
        name: libimobiledevice-latest_macOS
        path: libimobiledevice.tar
  build-windows:
    runs-on: windows-2019
    defaults:
      run:
        shell: msys2 {0}
    strategy:
      fail-fast: false
      matrix:
        include: [
          { msystem: MINGW64, arch: x86_64 },
          { msystem: MINGW32, arch: i686   }
        ]
    steps:
    - uses: msys2/setup-msys2@v2
      with:
        msystem: ${{ matrix.msystem }}
        release: false
        update: false
        install: >-
          base-devel
          git
          mingw-w64-${{ matrix.arch }}-gcc
          make
          libtool
          autoconf
          automake-wrapper
    - name: prepare environment
      run: |
          dest=`echo ${{ matrix.msystem }} |tr [:upper:] [:lower:]`
          echo "dest=$dest" >> $GITHUB_ENV
          echo "target_triplet=`gcc -dumpmachine`" >> $GITHUB_ENV
          git config --global core.autocrlf false
    - name: fetch libplist
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libplist-latest_${{ matrix.arch }}-${{ env.dest }}
        repo: libimobiledevice/libplist
    - name: fetch libusbmuxd
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libusbmuxd-latest_${{ matrix.arch }}-${{ env.dest }}
        repo: libimobiledevice/libusbmuxd
    - name: fetch libimobiledevice-glue
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libimobiledevice-glue-latest_${{ matrix.arch }}-${{ env.dest }}
        repo: libimobiledevice/libimobiledevice-glue
    - name: fetch libtatsu
      uses: dawidd6/action-download-artifact@v6
      with:
        github_token: ${{secrets.GITHUB_TOKEN}}
        workflow: build.yml
        name: libtatsu-latest_${{ matrix.arch }}-${{ env.dest }}
        repo: libimobiledevice/libtatsu
    - name: install external dependencies
      run: |
          mkdir extract
          for I in *.tar; do
            tar -C extract -xvf $I
          done
          cp -r extract/* /
    - uses: actions/checkout@v4
    - name: autogen
      run: ./autogen.sh CC=gcc CXX=g++ --enable-debug
    - name: make
      run: make
    - name: make install
      run: make install
    - name: prepare artifact
      run: |
          mkdir -p dest
          DESTDIR=`pwd`/dest make install
          tar -C dest -cf libimobiledevice.tar ${{ env.dest }}
    - name: publish artifact
      uses: actions/upload-artifact@v4
      with:
        name: libimobiledevice-latest_${{ matrix.arch }}-${{ env.dest }}
        path: libimobiledevice.tar
